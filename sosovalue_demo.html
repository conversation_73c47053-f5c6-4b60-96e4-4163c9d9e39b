<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoSoValue - 加密货币行情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-bg': '#0a0a0a',
                        'dark-card': '#1a1a1a',
                        'dark-border': '#2a2a2a',
                        'green-up': '#00d4aa',
                        'red-down': '#ff4747'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            background-color: #0a0a0a;
            color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
        }
        .top-banner {
            background: linear-gradient(90deg, #1a1a1a 0%, #2a2a2a 100%);
            border-bottom: 1px solid #333;
        }
        .table-header {
            background: #1a1a1a;
            border-bottom: 1px solid #333;
        }
        .table-row:hover {
            background: #1a1a1a;
        }
        .price-up {
            color: #00d4aa;
        }
        .price-down {
            color: #ff4747;
        }
        .price-neutral {
            color: #888;
        }
    </style>
</head>
<body class="bg-dark-bg text-white min-h-screen">
    <!-- 主布局容器 -->
    <div class="flex h-screen">
        <!-- 左侧导航栏 -->
        <div class="sidebar w-64 flex-shrink-0 border-r border-dark-border">
            <!-- Logo -->
            <div class="p-6 border-b border-dark-border">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                        <span class="text-black font-bold text-sm">S</span>
                    </div>
                    <span class="text-xl font-bold">SoSoValue</span>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="p-4 border-b border-dark-border">
                <div class="relative">
                    <input type="text" placeholder="搜索 SSI/Mag7/Meme/ETF/币种/指数/图表/研报" 
                           class="w-full bg-dark-card border border-dark-border rounded-lg px-4 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:border-blue-500">
                    <div class="absolute right-3 top-2.5">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 导航菜单 -->
            <nav class="p-4">
                <div class="space-y-2">
                    <div class="text-red-500 bg-red-500/10 px-3 py-2 rounded-lg font-medium">
                        <span class="text-sm">🔥 市场</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">📊 指数</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">📰 资讯</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">🎯 TokenBar®</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">📈 分析</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">🌍 宏观</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">👁️ 观察列表</span>
                    </div>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部横幅 -->
            <div class="top-banner p-4">
                <div class="flex items-center space-x-8 overflow-x-auto">
                    <div class="flex items-center space-x-2 whitespace-nowrap">
                        <span class="text-orange-500">🔥</span>
                        <span class="text-sm">加密货币</span>
                        <span class="text-orange-500 bg-orange-500/20 px-2 py-1 rounded text-xs">ETF</span>
                        <span class="text-sm">加密股</span>
                        <span class="text-red-500 bg-red-500/20 px-2 py-1 rounded text-xs">New</span>
                        <span class="text-sm">比特币储备</span>
                        <span class="text-red-500 bg-red-500/20 px-2 py-1 rounded text-xs">New</span>
                        <span class="text-sm">投融资</span>
                        <span class="text-sm">DEX池</span>
                    </div>
                </div>
            </div>

            <!-- 数据指标横幅 -->
            <div class="border-b border-dark-border p-4">
                <div class="flex items-center space-x-8 text-sm">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs font-bold">M</span>
                        </div>
                        <span class="text-sm font-medium">MAG7.ssi</span>
                        <span class="text-green-up bg-green-up/20 px-2 py-1 rounded text-xs">Buy</span>
                        <span class="text-white font-bold">$0.9751</span>
                        <span class="text-green-up text-sm">+2.05%</span>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs font-bold">D</span>
                        </div>
                        <span class="text-sm font-medium">DEFI.ssi</span>
                        <span class="text-green-up bg-green-up/20 px-2 py-1 rounded text-xs">Buy</span>
                        <span class="text-white font-bold">$0.7119</span>
                        <span class="text-green-up text-sm">+0.72%</span>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                            <span class="text-black text-xs font-bold">M</span>
                        </div>
                        <span class="text-sm font-medium">MEME.ssi</span>
                        <span class="text-green-up bg-green-up/20 px-2 py-1 rounded text-xs">Buy</span>
                        <span class="text-white font-bold">$0.6463</span>
                        <span class="text-green-up text-sm">+3.40%</span>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs font-bold">U</span>
                        </div>
                        <span class="text-sm font-medium">USSI</span>
                        <span class="text-green-up bg-green-up/20 px-2 py-1 rounded text-xs">Buy</span>
                        <span class="text-white font-bold">$1.0117</span>
                        <span class="text-red-down text-sm">-0.04%</span>
                    </div>
                    
                    <div class="flex items-center space-x-6 ml-auto">
                        <div>
                            <span class="text-gray-400">FGI</span>
                            <span class="text-green-up ml-2 font-bold">72</span>
                            <span class="text-green-up">Greed</span>
                        </div>
                        <div>
                            <span class="text-gray-400">资金费率</span>
                            <span class="text-white ml-2 font-bold">10.95%</span>
                            <span class="text-gray-400">(Yearly)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主表格区域 -->
            <div class="flex-1 overflow-hidden">
                <!-- 表格控制栏 -->
                <div class="p-4 border-b border-dark-border">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <span class="text-white font-medium">观察列表</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-gray-400 text-sm">排序:</span>
                                <select class="bg-dark-card border border-dark-border rounded px-3 py-1 text-sm text-white">
                                    <option>市值</option>
                                    <option>涨幅最大</option>
                                    <option>跌幅最大</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="p-2 hover:bg-dark-card rounded">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 加密货币表格 -->
                <div class="overflow-auto h-full">
                    <table class="w-full">
                        <thead class="table-header sticky top-0">
                            <tr class="text-left text-sm text-gray-400">
                                <th class="px-4 py-3 font-medium">#</th>
                                <th class="px-4 py-3 font-medium">币种</th>
                                <th class="px-4 py-3 font-medium text-right">价格</th>
                                <th class="px-4 py-3 font-medium text-right">24H涨跌 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">24H成交量 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">市值 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">FDV ↓</th>
                                <th class="px-4 py-3 font-medium text-right">月回报率 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">年回报率 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">年初至今 ↓</th>
                            </tr>
                        </thead>
                        <tbody id="crypto-tbody" class="text-sm">
                            <!-- 数据将在这里插入 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 格式化价格
        function formatPrice(price) {
            if (price >= 1000) {
                return `$${price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
            } else if (price >= 1) {
                return `$${price.toFixed(4)}`;
            } else {
                return `$${price.toFixed(6)}`;
            }
        }

        // 格式化市值
        function formatMarketCap(marketCap) {
            if (marketCap >= 1e12) {
                return `$${(marketCap / 1e12).toFixed(2)}T`;
            } else if (marketCap >= 1e9) {
                return `$${(marketCap / 1e9).toFixed(2)}B`;
            } else if (marketCap >= 1e6) {
                return `$${(marketCap / 1e6).toFixed(2)}M`;
            } else {
                return `$${marketCap.toLocaleString()}`;
            }
        }

        // 格式化交易量
        function formatVolume(volume) {
            if (volume >= 1e9) {
                return `$${(volume / 1e9).toFixed(2)}B`;
            } else if (volume >= 1e6) {
                return `$${(volume / 1e6).toFixed(2)}M`;
            } else if (volume >= 1e3) {
                return `$${(volume / 1e3).toFixed(2)}K`;
            } else {
                return `$${volume.toFixed(2)}`;
            }
        }

        // 格式化百分比
        function formatPercentage(percentage) {
            const sign = percentage >= 0 ? '+' : '';
            return `${sign}${percentage.toFixed(2)}%`;
        }

        // 模拟加密货币数据
        function generateMockData() {
            const cryptos = [
                { symbol: 'BTC', name: 'Bitcoin', price: 118121.9, change24h: 1.91, volume: 42.22, marketCap: 2350.36, image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png' },
                { symbol: 'ETH', name: 'Ethereum', price: 3743.63, change24h: 1.27, volume: 32.63, marketCap: 451.9, image: 'https://assets.coingecko.com/coins/images/279/large/ethereum.png' },
                { symbol: 'XRP', name: 'XRP', price: 3.1865, change24h: 3.00, volume: 5.23, marketCap: 188.77, image: 'https://assets.coingecko.com/coins/images/44/large/xrp-symbol-white-128.png' },
                { symbol: 'USDT', name: 'Tether', price: 1.00031, change24h: 0.03, volume: 98.05, marketCap: 163.35, image: 'https://assets.coingecko.com/coins/images/325/large/Tether.png' },
                { symbol: 'BNB', name: 'Binance Coin', price: 781.42, change24h: 1.16, volume: 1.23, marketCap: 108.84, image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png' },
                { symbol: 'SOL', name: 'Solana', price: 187.41, change24h: 4.01, volume: 10.53, marketCap: 100.88, image: 'https://assets.coingecko.com/coins/images/4128/large/solana.png' },
                { symbol: 'USDC', name: 'USD Coin', price: 0.9996, change24h: -0.02, volume: 8.32, marketCap: 64.2, image: 'https://assets.coingecko.com/coins/images/6319/large/USD_Coin_icon.png' },
                { symbol: 'DOGE', name: 'Dogecoin', price: 0.23811, change24h: 3.53, volume: 5.86, marketCap: 35.77, image: 'https://assets.coingecko.com/coins/images/5/large/dogecoin.png' },
                { symbol: 'STETH', name: 'Lido Staked Ether', price: 3739.5, change24h: 1.32, volume: 35.88, marketCap: 33.69, image: 'https://assets.coingecko.com/coins/images/13442/large/steth_logo.png' },
                { symbol: 'TRX', name: 'TRON', price: 0.3185, change24h: 1.05, volume: 2.41, marketCap: 30.17, image: 'https://assets.coingecko.com/coins/images/1094/large/tron-logo.png' },
                { symbol: 'ADA', name: 'Cardano', price: 0.8293, change24h: 3.75, volume: 1.25, marketCap: 29.99, image: 'https://assets.coingecko.com/coins/images/975/large/cardano.png' },
                { symbol: 'WBTC', name: 'Wrapped Bitcoin', price: 118055.3, change24h: 1.90, volume: 171.52, marketCap: 15.21, image: 'https://assets.coingecko.com/coins/images/7598/large/wrapped_bitcoin_wbtc.png' },
                { symbol: 'HYPE', name: 'Hyperliquid', price: 44.39, change24h: 2.94, volume: 255.16, marketCap: 14.81, image: 'https://assets.coingecko.com/coins/images/38976/large/hype.png' },
                { symbol: 'SUI', name: 'Sui', price: 4.0718, change24h: 8.87, volume: 1.79, marketCap: 14.07, image: 'https://assets.coingecko.com/coins/images/26375/large/sui.png' },
                { symbol: 'XLM', name: 'Stellar', price: 0.4386, change24h: 4.28, volume: 457.13, marketCap: 13.67, image: 'https://assets.coingecko.com/coins/images/100/large/Stellar_symbol_black_RGB.png' },
                { symbol: 'LINK', name: 'Chainlink', price: 18.32, change24h: 1.27, volume: 510.03, marketCap: 12.42, image: 'https://assets.coingecko.com/coins/images/877/large/chainlink-new-logo.png' },
                { symbol: 'WBETH', name: 'Wrapped Beacon ETH', price: 4025.338, change24h: 0.04, volume: 5.07, marketCap: 11.93, image: 'https://assets.coingecko.com/coins/images/30061/large/wbeth.png' },
                { symbol: 'HBAR', name: 'Hedera', price: 0.279, change24h: 13.83, volume: 849.94, marketCap: 11.83, image: 'https://assets.coingecko.com/coins/images/3441/large/Hedera_Hashgraph_logo.png' },
                { symbol: 'BCH', name: 'Bitcoin Cash', price: 557, change24h: 2.50, volume: 371.29, marketCap: 11.09, image: 'https://assets.coingecko.com/coins/images/780/large/bitcoin-cash-circle.png' },
                { symbol: 'AVAX', name: 'Avalanche', price: 24.34, change24h: 2.92, volume: 460.07, marketCap: 10.28, image: 'https://assets.coingecko.com/coins/images/12559/large/Avalanche_Circle_RedWhite_Trans.png' }
            ];

            return cryptos.map((crypto, index) => ({
                ...crypto,
                rank: index + 1,
                monthlyChange: (Math.random() * 200 - 100).toFixed(2),
                yearlyChange: (Math.random() * 500 - 250).toFixed(2),
                ytdChange: (Math.random() * 300 - 150).toFixed(2)
            }));
        }

        // 渲染加密货币表格
        function renderCryptoTable(data) {
            const tbody = document.getElementById('crypto-tbody');
            tbody.innerHTML = '';

            data.forEach((crypto, index) => {
                const row = document.createElement('tr');
                row.className = 'table-row border-b border-dark-border hover:bg-dark-card/50';

                const changeClass = crypto.change24h >= 0 ? 'price-up' : 'price-down';
                const monthlyClass = crypto.monthlyChange >= 0 ? 'price-up' : 'price-down';
                const yearlyClass = crypto.yearlyChange >= 0 ? 'price-up' : 'price-down';
                const ytdClass = crypto.ytdChange >= 0 ? 'price-up' : 'price-down';

                row.innerHTML = `
                    <td class="px-4 py-3 text-gray-400">${crypto.rank}</td>
                    <td class="px-4 py-3">
                        <div class="flex items-center">
                            <img class="h-6 w-6 rounded-full mr-3" src="${crypto.image}" alt="${crypto.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTIiIGZpbGw9IiM2NjdFRUEiLz4KPHN2ZyB4PSI2IiB5PSI2IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01aDNWOGg0djRoM2wtNSA1eiIgZmlsbD0iI0ZGRiIvPgo8L3N2Zz4KPC9zdmc+'">
                            <div>
                                <div class="text-white font-medium">${crypto.symbol}</div>
                                <div class="text-gray-400 text-xs">${crypto.name}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-4 py-3 text-right text-white font-mono">
                        ${formatPrice(crypto.price)}
                    </td>
                    <td class="px-4 py-3 text-right ${changeClass} font-mono">
                        ${formatPercentage(crypto.change24h)}
                    </td>
                    <td class="px-4 py-3 text-right text-white font-mono">
                        ${formatVolume(crypto.volume * 1e9)}
                    </td>
                    <td class="px-4 py-3 text-right text-white font-mono">
                        ${formatMarketCap(crypto.marketCap * 1e9)}
                    </td>
                    <td class="px-4 py-3 text-right text-white font-mono">
                        ${formatMarketCap(crypto.marketCap * 1e9)}
                    </td>
                    <td class="px-4 py-3 text-right ${monthlyClass} font-mono">
                        ${formatPercentage(crypto.monthlyChange)}
                    </td>
                    <td class="px-4 py-3 text-right ${yearlyClass} font-mono">
                        ${formatPercentage(crypto.yearlyChange)}
                    </td>
                    <td class="px-4 py-3 text-right ${ytdClass} font-mono">
                        ${formatPercentage(crypto.ytdChange)}
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // 加载数据
        function loadData() {
            try {
                // 使用模拟数据
                const data = generateMockData();
                renderCryptoTable(data);

            } catch (error) {
                console.error('Error loading data:', error);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
        });
    </script>
</body>
</html>
