<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoSoValue - 加密货币行情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-bg': '#0a0a0a',
                        'dark-card': '#1a1a1a',
                        'dark-border': '#2a2a2a',
                        'green-up': '#00d4aa',
                        'red-down': '#ff4747'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            background-color: #0a0a0a;
            color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .crypto-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
        }
        .top-banner {
            background: linear-gradient(90deg, #1a1a1a 0%, #2a2a2a 100%);
            border-bottom: 1px solid #333;
        }
        .table-header {
            background: #1a1a1a;
            border-bottom: 1px solid #333;
        }
        .table-row:hover {
            background: #1a1a1a;
        }
        .price-up {
            color: #00d4aa;
        }
        .price-down {
            color: #ff4747;
        }
        .price-neutral {
            color: #888;
        }
    </style>
</head>
<body class="bg-dark-bg text-white min-h-screen">
    <!-- 主布局容器 -->
    <div class="flex h-screen">
        <!-- 左侧导航栏 -->
        <div class="sidebar w-64 flex-shrink-0 border-r border-dark-border">
            <!-- Logo -->
            <div class="p-6 border-b border-dark-border">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-white rounded mr-3 flex items-center justify-center">
                        <span class="text-black font-bold text-sm">S</span>
                    </div>
                    <span class="text-xl font-bold">SoSoValue</span>
                </div>
            </div>

            <!-- 搜索框 -->
            <div class="p-4 border-b border-dark-border">
                <div class="relative">
                    <input type="text" placeholder="搜索 SSI/Mag7/Meme/ETF/币种/指数/图表/研报"
                           class="w-full bg-dark-card border border-dark-border rounded-lg px-4 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:border-blue-500">
                    <div class="absolute right-3 top-2.5">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="p-4">
                <div class="space-y-2">
                    <div class="text-red-500 bg-red-500/10 px-3 py-2 rounded-lg font-medium">
                        <span class="text-sm">🔥 市场</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">📊 指数</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">📰 资讯</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">🎯 TokenBar®</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">📈 分析</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">🌍 宏观</span>
                    </div>
                    <div class="text-gray-400 px-3 py-2 hover:text-white hover:bg-dark-card rounded-lg cursor-pointer">
                        <span class="text-sm">👁️ 观察列表</span>
                    </div>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部横幅 -->
            <div class="top-banner p-4">
                <div class="flex items-center space-x-8 overflow-x-auto">
                    <div class="flex items-center space-x-2 whitespace-nowrap">
                        <span class="text-orange-500">🔥</span>
                        <span class="text-sm">加密货币</span>
                        <span class="text-orange-500 bg-orange-500/20 px-2 py-1 rounded text-xs">ETF</span>
                        <span class="text-sm">加密股</span>
                        <span class="text-red-500 bg-red-500/20 px-2 py-1 rounded text-xs">New</span>
                        <span class="text-sm">比特币储备</span>
                        <span class="text-red-500 bg-red-500/20 px-2 py-1 rounded text-xs">New</span>
                        <span class="text-sm">投融资</span>
                        <span class="text-sm">DEX池</span>
                    </div>
                </div>
            </div>

            <!-- 数据指标横幅 -->
            <div class="border-b border-dark-border p-4">
                <div class="flex items-center space-x-8 text-sm">
                    <div class="flex items-center space-x-2">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTIiIGZpbGw9IiNGNzkyMUEiLz4KPHN2ZyB4PSI2IiB5PSI2IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01aDNWOGg0djRoM2wtNSA1eiIgZmlsbD0iI0ZGRiIvPgo8L3N2Zz4KPC9zdmc+"
                             alt="BTC" class="w-6 h-6">
                        <span class="text-sm font-medium">MAG7.ssi</span>
                        <span class="text-green-up bg-green-up/20 px-2 py-1 rounded text-xs">Buy</span>
                        <span class="text-white font-bold">$0.9751</span>
                        <span class="text-green-up text-sm">+2.05%</span>
                    </div>

                    <div class="flex items-center space-x-2">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTIiIGZpbGw9IiM2MjdFRUEiLz4KPHN2ZyB4PSI2IiB5PSI2IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01aDNWOGg0djRoM2wtNSA1eiIgZmlsbD0iI0ZGRiIvPgo8L3N2Zz4KPC9zdmc+"
                             alt="ETH" class="w-6 h-6">
                        <span class="text-sm font-medium">DEFI.ssi</span>
                        <span class="text-green-up bg-green-up/20 px-2 py-1 rounded text-xs">Buy</span>
                        <span class="text-white font-bold">$0.7119</span>
                        <span class="text-green-up text-sm">+0.72%</span>
                    </div>

                    <div class="flex items-center space-x-2">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTIiIGZpbGw9IiNGRkQ3MDAiLz4KPHN2ZyB4PSI2IiB5PSI2IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01aDNWOGg0djRoM2wtNSA1eiIgZmlsbD0iIzAwMCIvPgo8L3N2Zz4KPC9zdmc+"
                             alt="MEME" class="w-6 h-6">
                        <span class="text-sm font-medium">MEME.ssi</span>
                        <span class="text-green-up bg-green-up/20 px-2 py-1 rounded text-xs">Buy</span>
                        <span class="text-white font-bold">$0.6463</span>
                        <span class="text-green-up text-sm">+3.40%</span>
                    </div>

                    <div class="flex items-center space-x-2">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTIiIGZpbGw9IiMwMEQ0QUEiLz4KPHN2ZyB4PSI2IiB5PSI2IiB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01aDNWOGg0djRoM2wtNSA1eiIgZmlsbD0iI0ZGRiIvPgo8L3N2Zz4KPC9zdmc+"
                             alt="USSI" class="w-6 h-6">
                        <span class="text-sm font-medium">USSI</span>
                        <span class="text-green-up bg-green-up/20 px-2 py-1 rounded text-xs">Buy</span>
                        <span class="text-white font-bold">$1.0117</span>
                        <span class="text-red-down text-sm">-0.04%</span>
                    </div>

                    <div class="flex items-center space-x-6 ml-auto">
                        <div>
                            <span class="text-gray-400">FGI</span>
                            <span class="text-green-up ml-2 font-bold">72</span>
                            <span class="text-green-up">Greed</span>
                        </div>
                        <div>
                            <span class="text-gray-400">资金费率</span>
                            <span class="text-white ml-2 font-bold">10.95%</span>
                            <span class="text-gray-400">(Yearly)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主表格区域 -->
            <div class="flex-1 overflow-hidden">
                <!-- 表格控制栏 -->
                <div class="p-4 border-b border-dark-border">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <span class="text-white font-medium">观察列表</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-gray-400 text-sm">排序:</span>
                                <select class="bg-dark-card border border-dark-border rounded px-3 py-1 text-sm text-white">
                                    <option>市值</option>
                                    <option>涨幅最大</option>
                                    <option>跌幅最大</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="p-2 hover:bg-dark-card rounded">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 加密货币表格 -->
                <div class="overflow-auto h-full">
                    <table class="w-full">
                        <thead class="table-header sticky top-0">
                            <tr class="text-left text-sm text-gray-400">
                                <th class="px-4 py-3 font-medium">#</th>
                                <th class="px-4 py-3 font-medium">币种</th>
                                <th class="px-4 py-3 font-medium text-right">价格</th>
                                <th class="px-4 py-3 font-medium text-right">24H涨跌 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">24H成交量 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">市值 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">FDV ↓</th>
                                <th class="px-4 py-3 font-medium text-right">月回报率 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">年回报率 ↓</th>
                                <th class="px-4 py-3 font-medium text-right">年初至今 ↓</th>
                            </tr>
                        </thead>
                        <tbody id="crypto-tbody" class="text-sm">
                            <!-- 数据将在这里插入 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 格式化价格
        function formatPrice(price) {
            if (price >= 1000) {
                return `$${price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
            } else if (price >= 1) {
                return `$${price.toFixed(4)}`;
            } else {
                return `$${price.toFixed(6)}`;
            }
        }

        // 格式化市值
        function formatMarketCap(marketCap) {
            if (marketCap >= 1e12) {
                return `$${(marketCap / 1e12).toFixed(2)}T`;
            } else if (marketCap >= 1e9) {
                return `$${(marketCap / 1e9).toFixed(2)}B`;
            } else if (marketCap >= 1e6) {
                return `$${(marketCap / 1e6).toFixed(2)}M`;
            } else {
                return `$${marketCap.toLocaleString()}`;
            }
        }

        // 格式化交易量
        function formatVolume(volume) {
            if (volume >= 1e9) {
                return `$${(volume / 1e9).toFixed(2)}B`;
            } else if (volume >= 1e6) {
                return `$${(volume / 1e6).toFixed(2)}M`;
            } else if (volume >= 1e3) {
                return `$${(volume / 1e3).toFixed(2)}K`;
            } else {
                return `$${volume.toFixed(2)}`;
            }
        }

        // 格式化百分比
        function formatPercentage(percentage) {
            const sign = percentage >= 0 ? '+' : '';
            return `${sign}${percentage.toFixed(2)}%`;
        }

        // 获取百分比颜色
        function getPercentageColor(percentage) {
            if (percentage > 0) return 'text-green-600';
            if (percentage < 0) return 'text-red-600';
            return 'text-gray-600';
        }

        // 加载数据
        async function loadData() {
            try {
                document.getElementById('loading').classList.remove('hidden');
                document.getElementById('error').classList.add('hidden');
                document.getElementById('overview').classList.add('hidden');
                document.getElementById('crypto-table').classList.add('hidden');

                const response = await fetch('http://localhost:8000/api/crypto/latest?limit=50');
                if (!response.ok) {
                    throw new Error('Failed to fetch data');
                }

                const data = await response.json();
                
                // 计算市场统计
                const totalMarketCap = data.reduce((sum, crypto) => sum + crypto.market_cap, 0);
                const totalVolume = data.reduce((sum, crypto) => sum + crypto.volume_usd_24h, 0);
                const gainersCount = data.filter(crypto => crypto.price_change_percent_24h > 0).length;
                const losersCount = data.filter(crypto => crypto.price_change_percent_24h < 0).length;

                // 更新概览数据
                document.getElementById('total-market-cap').textContent = formatMarketCap(totalMarketCap);
                document.getElementById('total-volume').textContent = formatVolume(totalVolume);
                document.getElementById('gainers-count').textContent = gainersCount;
                document.getElementById('losers-count').textContent = losersCount;

                // 生成表格行
                const tbody = document.getElementById('crypto-tbody');
                tbody.innerHTML = '';

                data.forEach((crypto, index) => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50 transition-colors';
                    
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${index + 1}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="crypto-icon h-10 w-10 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold text-white">${crypto.symbol.charAt(0)}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">${crypto.symbol}</div>
                                    <div class="text-sm text-gray-500">${crypto.symbol}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                            ${formatPrice(crypto.current_price)}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium ${getPercentageColor(crypto.price_change_percent_24h)}">
                            ${formatPercentage(crypto.price_change_percent_24h)}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium ${getPercentageColor(crypto.price_change_percent_1w)}">
                            ${formatPercentage(crypto.price_change_percent_1w)}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                            ${formatVolume(crypto.volume_usd_24h)}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                            ${formatMarketCap(crypto.market_cap)}
                        </td>
                    `;
                    
                    tbody.appendChild(row);
                });

                // 显示内容
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('overview').classList.remove('hidden');
                document.getElementById('crypto-table').classList.remove('hidden');

            } catch (error) {
                console.error('Error loading data:', error);
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('error').classList.remove('hidden');
            }
        }

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', loadData);

        // 每30秒刷新一次数据
        setInterval(loadData, 30000);
    </script>
</body>
</html>
